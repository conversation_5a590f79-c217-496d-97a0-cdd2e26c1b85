// ADS value objects representing aggregated test item results
// These structures contain statistical metrics and aggregated data
pub mod die_final_info;
pub mod test_item_bin;
pub mod test_item_detail;
pub mod test_item_program;
pub mod test_item_site;
pub mod test_item_site_bin;

use crate::utils::decimal::{Decimal38_18, IntoDecimal38_18};
pub use die_final_info::DieFinalInfo;
pub use test_item_bin::TestItemBin;
pub use test_item_program::TestItemProgram;
pub use test_item_site::TestItemSite;
pub use test_item_site_bin::TestItemSiteBin;

pub fn to_decimal(val: f64) -> Option<Decimal38_18> {
    if val.is_finite() && !val.is_nan() {
        Some(val.into_decimal38_18())
    } else {
        None
    }
}

// TODO 排序
pub fn mk_string_distinct(values: Vec<&str>) -> String {
    let unique: std::collections::HashSet<&str> = values.into_iter().collect();
    unique.into_iter().collect::<Vec<_>>().join(",")
}
